<?php include(APPPATH . 'views/feesv2/reports/components/report_header.php'); ?>
<div class="col-md-12">
    <div class="col-md-3 form-group" style="min-width: 200px;">
        <p class="label-text">Filter By</p>
        <select required="" class="form-control" id="filter_by" name="" onchange="filter_by()">
            <option value="date">Date Range</option>
            <option value="name">Student Name</option>
        </select>
    </div>

    <div class="col-md-3 form-group mb-3" id="date" style="min-width: 250px;">
        <p class="label-text">Date Range</p>
        <div id="reportrange" class="dtrange w-100 ps-3 py-1">
            <span></span>
            <input type="hidden" id="from_date">
            <input type="hidden" id="to_date">
        </div>
    </div>

    <div class="col-md-3 form-group mb-3" id="search_by_name" style="display: none; min-width: 250px;">
        <p class="label-text">Search By Name</p>
        <input type="text" name="by_name" id="by_name" class="form-control">
    </div>

    <div class="col-md-6 form-group d-flex align-items-end" style="margin-top: 28px;margin-left: 40rem;">
        <button class="btn btn-primary" id="search" type="button" onclick="get_fees_history_data();">Get Report</button>
    </div>

    <!-- <div class="col-md-2 form-group">
                    <label class="control-label" style="padding-left: 10px;">Class Section</label>
                    <div class="form-group">
                        <div class="col-md-12">
                            <select required="" class="form-control" id="class_section" name="class_section">
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-md-2 form-group">
                    <label class="control-label" style="padding-left: 10px;">Student Name</label>
                    <div class="form-group">
                        <div class="col-md-12">
                            <select required="" class="form-control" id="stud_id" name="stud_id">
                                <option value="">All Students</option>
                            </select>
                        </div>
                    </div>
                </div> -->

</div>
</div>
<div class="card-body">
    <?php $this->load->helper('reports_datatable');
    echo progress_bar(); ?>

    <?php $this->load->helper('reports_datatable'); ?>
    <div id="no-data-template" style="display: none;">
        <?php echo no_data_message(); ?>
    </div>

    <div id="printArea">
        <div id="print_visible" style="display: none;" class="text-center">
            <h3 style="margin-bottom: 0.25rem; font-size: 1.5rem; font-family: 'Poppins', serif;">
                <?php echo htmlspecialchars($this->settings->getSetting('school_name')); ?>
            </h3>
            <h4 style="margin-top: 0.25rem;font-size: 1.3rem;font-weight: bold;letter-spacing: 1px;color: #222;text-transform: uppercase;border-top: 1px solid #444;border-bottom: 1px solid #444;padding: 0.5rem 0;font-family: 'Poppins', serif;">
                Fees Edit History Report
            </h4>
        </div>

        <div class="mx-3">
            <div class="table-responsive" id="report_width_container">
                <div id="history_table"></div>
            </div>
        </div>
        <style>
            #report_width_container {
                padding-left: 30px;
                padding-right: 25px;
            }
        </style>
    </div>
</div>
</div>
</div>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<script>
    $(document).ready(function() {
        // get_class_section();
        get_fees_history_data();
    });

    function filter_by() {
        var filter_by = $('#filter_by').val();
        if (filter_by == 'name') {
            $('#date').hide();
            $('#search_by_name').show();
        } else {
            $('#date').show();
            $('#search_by_name').hide();
            $('#by_name').val('');
        }
    }

    $("#reportrange").daterangepicker({
        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(6, 'days'), moment()],
            'Last 30 Days': [moment().subtract(29, 'days'), moment()],
            'This Month': [moment().startOf('month'), moment()],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
        },
        opens: 'right',
        buttonClasses: ['btn btn-default'],
        applyClass: 'btn-small btn-primary',
        cancelClass: 'btn-small',
        format: 'DD-MM-YYYY',
        separator: ' to ',
        startDate: moment().subtract(6, 'days'),
        endDate: moment()
    }, function(start, end) {
        $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        $('#from_date').val(start.format('DD-MM-YYYY'));
        $('#to_date').val(end.format('DD-MM-YYYY'));
    });

    $("#reportrange span").html(moment().subtract(6, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
    $('#from_date').val(moment().subtract(6, 'days').format('DD-MM-YYYY'));
    $('#to_date').val(moment().format('DD-MM-YYYY'));

    function get_class_section() {
        $.ajax({
            url: '<?php echo site_url('reports/student/Student_docs_controller/get_class_section_names'); ?>',
            type: "post",
            success: function(data) {
                var clsSection = $.parseJSON(data);
                option = '<option value="">Class Section</option>';
                for (i = 0; i < clsSection.length; i++) {
                    option += '<option value="' + clsSection[i].cId + '_' + clsSection[i].csId + '">' +
                        clsSection[i].classSection + '</option>'
                }
                $('#class_section').html(option);
            },

            error: function(err) {
                console.log(err);
            }
        });
    }

    $("#class_section").on('change', function() {
        // var cls_section = $('#class_section').val();
        $.ajax({
            url: '<?php echo site_url('reports/student/Student_docs_controller/get_class_section_student_data'); ?>',
            type: "post",
            data: {
                'cls_section': cls_section
            },
            success: function(data) {
                var stu_data = $.parseJSON(data);
                option = '';
                option += '<option value="">All Students</option>';
                for (i = 0; i < stu_data.length; i++) {
                    option += '<option value="' + stu_data[i].sId + '">' + stu_data[i].sName +
                        '</option>'
                }
                $('#stud_id').html(option);
            },
            error: function(err) {
                console.log(err);
            }
        });
    });

    function get_fees_history_data() {
        // Show progress bar and disable button
        $('.progress').show();
        $('#search').prop('disabled', true).text('Please wait..');
        $('#history_table').html('');

        var from_date = $('#from_date').val();
        var to_date = $('#to_date').val();
        var name = $('#by_name').val();

        $.ajax({
            url: '<?php echo site_url('feesv2/reports_v2/get_fees_edit_history_data'); ?>',
            type: "post",
            data: {
                // 'cls_section': cls_section,
                // 'stu_id': stu_id
                'name': name,
                'from_date': from_date,
                'to_date': to_date
            },
            success: function(data) {
                var stu_data = $.parseJSON(data);
                if (stu_data != '') {
                    $('#history_table').html(construct_table(stu_data));
                    initializeDataTable();
                } else {
                    $('#history_table').html($('#no-data-template').html());
                }

                // Hide progress bar and re-enable button
                $('.progress').hide();
                $('#search').prop('disabled', false).text('Get Report');
            },
            error: function(err) {
                console.log(err);
                // Hide progress bar and re-enable button on error
                $('.progress').hide();
                $('#search').prop('disabled', false).text('Get Report');
                $('#history_table').html('<div class="alert alert-danger">Error loading data. Please try again.</div>');
            }
        });
    }

    function construct_table(data) {
        var html = '';
        html += `<table class="table table-bordered" id="data_table" style="width: 100%">
                <thead>
                <tr>
                <th>#</th>
                <th>Student Name</th>
                <th>Old Data</th>
                <th>Updated Data</th>
                <th>Edited By</th>
                <th>Edited On</th>
                </tr>
                </thead>
                <tbody>`;
        for (var i = 0; i < data.length; i++) {
            html += '<tr>';
            html += `<td>${i+1}</td>`;
            html += `<td>${data[i].student_name}</td>`;
            if (data[i].old_value == null) {
                data[i].old_value = '';
            }
            if (data[i].edited_by == ' ') {
                data[i].edited_by = 'Admin';
            }
            html += `<td>${data[i].old_value}</td>`;
            html += `<td>${data[i].new_value}</td>`;
            html += `<td>${data[i].edited_by}</td>`;
            html += `<td>${data[i].edited_on}</td>`;
            html += '</tr>';
        }
        html += '</tbody>';
        html += '</table>';

        return html;
    }

    function initializeDataTable() {
        // Check if table exists before initializing
        if (!$('#data_table').length) {
            console.warn('Table element #data_table not found');
            return;
        }

        // Destroy existing DataTable if it exists
        if ($.fn.DataTable.isDataTable('#data_table')) {
            $('#data_table').DataTable().destroy();
            $('#data_table').empty();
        }

        let table = $('#data_table').DataTable({
            ordering: false,
            searching: true,
            paging: true,
            pageLength: 10,
            pagingType: 'full_numbers',
            dom: '<"row align-items-center mb-2" <"col-md-6"l> <"col-md-6 d-flex justify-content-end gap-2"fB> >rt<"row align-items-center mt-4" <"col-md-6 mb-2"i> <"col-md-6 d-flex justify-content-end mb-2"p> >',
            info: true,
            'columnDefs': [{
                orderable: false,
                targets: '_all'
            }],
            fixedHeader: true,
            scrollY: 400,
            scrollX: true,
            scrollCollapse: true,
            autoWidth: true,
            language: {
                search: "",
                searchPlaceholder: "Enter Search..",
                lengthMenu: "Show _MENU_ ",
                info: "Showing _START_ to _END_ of _TOTAL_ ",
                paginate: {
                    first: "&laquo;",
                    last: "&raquo;",
                    next: "Next &rsaquo;",
                    previous: "&lsaquo; Previous"
                },
                emptyTable: "No data available in table",
                zeroRecords: "No matching records found"
            },
            lengthMenu: [
                [10, 25, 50, -1],
                [10, 25, 50, "All"]
            ],
            buttons: [{
                    extend: 'print',
                    text: `<button class="btn btn-outline-primary" id="expbtns" style="margin-right:-7px;"><?= $this->load->view('svg_icons/print.svg', [], true); ?> Print</button>`,
                    title: '',
                    footer: true,
                    exportOptions: {
                        columns: ':visible',
                    },
                    customize: function(win) {
                        $(win.document.body)
                            .css('font-family', "'Poppins', sans-serif")
                            .css('font-size', '10pt')
                            .css('padding', '10px');

                        // Add header from print_visible div
                        var headerHtml = $('#print_visible').html();
                        if (headerHtml) {
                            $(win.document.body).prepend('<div class="text-center">' + headerHtml + '</div>');
                        }

                        $(win.document.head).append(`
                    <style>
                        @page {
                        size: auto;
                        margin: 12mm;
                        }

                        body {
                        font-family: 'Poppins', sans-serif;
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                        color: #333;
                        background: #fff;
                        }

                        h2, h3 {
                        text-align: center;
                        margin-bottom: 15px;
                        font-weight: 500;
                        }

                        h4 {
                        text-align: center;
                        margin-top: 0.25rem;
                        font-size: 1.3rem;
                        font-weight: bold;
                        letter-spacing: 1px;
                        color: #222;
                        text-transform: uppercase;
                        border-top: 1px solid #444;
                        border-bottom: 1px solid #444;
                        padding: 0.5rem 0;
                        font-family: 'Poppins', serif;
                        }

                        table {
                        border-collapse: collapse !important;
                        width: 100% !important;
                        margin-top: 20px;
                        font-size: 10pt;
                        color: #333;
                        }

                        th, td {
                        border: 1px solid #ccc !important;
                        padding: 8px 12px;
                        text-align: left;
                        vertical-align: middle;
                        }

                        th {
                        background-color: #f4f7fc !important;
                        font-weight: 600;
                        color: #333;
                        }

                        .table-bordered {
                        width: 100% !important;
                        }

                        tfoot th {
                        background-color: #f9f9f9;
                        font-weight: 600;
                        }
                    </style>
                    `);
                    },
                },
                {
                    extend: 'excelHtml5',
                    text: `<button class="btn btn-outline-primary" id="expbtns"><?= $this->load->view('svg_icons/excel.svg', [], true); ?> Excel</button>`,
                    title: 'Fees Edit History Report',
                    footer: true,
                    messageTop: '<?php echo htmlspecialchars($this->settings->getSetting('school_name')); ?> ',
                    exportOptions: {
                        columns: ':visible',
                    },
                },
            ],
        });

        // Custom search styling
        setTimeout(() => {
            const $input = $('.dt-search input[type="search"]');
            if ($input.length) {
                if ($input.parent().hasClass('search-box')) {
                    $input.unwrap();
                }
                $input.siblings('.bi-search').remove();
                $input.addClass('input-search');
                $input.wrap('<div class="search-box position-relative"></div>');
                $input.parent().prepend('<i class="bi bi-search"></i>');
                const searchWrapper = $input.closest('.search-box');
                if ($('.dt-search').length) {
                    $('.dt-search').empty().append(searchWrapper);
                }
                $input.off('input').on('input', function() {
                    if (table && typeof table.search === 'function') {
                        table.search(this.value).draw();
                    }
                });
            }
        }, 0);

        const $filter = $('#data_table_filter');
        if ($filter.length) {
            const $input = $filter.find('input');

            $filter.find('label').contents().filter(function() {
                return this.nodeType === 3;
            }).remove();

            if ($input.length) {
                $input.attr('placeholder', 'Enter Search..');
            }
            $filter.addClass('custom-search-box');
            $filter.find('label').wrapInner('<div class="search-box"></div>');
            $filter.find('.search-box').prepend('<i class="bi bi-search"></i>');
        }

        add_scroller('report_width_container');
    }

    function add_scroller(containerId) {
        const container = document.getElementById(containerId);
        if (container && container.style) {
            container.style.overflowX = 'auto';
            container.style.position = 'relative';
        }
    }
</script>